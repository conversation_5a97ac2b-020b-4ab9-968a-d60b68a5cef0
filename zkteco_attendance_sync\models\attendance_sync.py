# -*- coding: utf-8 -*-
# توسيع نموذج الحضور لإضافة معلومات الجهاز

from odoo import models, fields, api
from datetime import timedelta


class HrAttendance(models.Model):
    _inherit = 'hr.attendance'

    device_id = fields.Many2one(
        'zkteco.device',
        string='Source Device',
        help='ZKTeco device that recorded this attendance',
        readonly=True
    )
    
    is_late = fields.Boolean(
        string='Late Arrival',
        compute='_compute_attendance_status',
        store=True,
        help='True if employee arrived late'
    )
    
    late_minutes = fields.Integer(
        string='Late Minutes',
        compute='_compute_attendance_status',
        store=True,
        help='Number of minutes late'
    )

    @api.depends('check_in', 'device_id', 'employee_id')
    def _compute_attendance_status(self):
        """حساب حالة التأخير"""
        for record in self:
            record.is_late = False
            record.late_minutes = 0
            
            if not record.check_in or not record.device_id:
                continue
                
            # Get work start time from device configuration
            work_start_time = record.device_id.work_start_time
            late_tolerance = record.device_id.late_tolerance
            
            # Convert work start time to datetime
            check_in_time = record.check_in
            work_start_datetime = check_in_time.replace(
                hour=int(work_start_time),
                minute=int((work_start_time % 1) * 60),
                second=0,
                microsecond=0
            )
            
            # Add tolerance
            tolerance_datetime = work_start_datetime + timedelta(minutes=late_tolerance)
            
            if check_in_time > tolerance_datetime:
                record.is_late = True
                time_diff = check_in_time - work_start_datetime
                record.late_minutes = int(time_diff.total_seconds() / 60)
