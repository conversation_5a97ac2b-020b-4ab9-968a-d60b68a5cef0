# -*- coding: utf-8 -*-
# نموذج إعدادات أجهزة ZKTeco - يحتوي على معلومات الاتصال والإعدادات

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import logging

_logger = logging.getLogger(__name__)

try:
    from zk import ZK
except ImportError:
    _logger.warning("pyzk library not found. Please install it using: pip install pyzk")
    ZK = None


class ZKTecoDevice(models.Model):
    _name = 'zkteco.device'
    _description = 'ZKTeco Biometric Device Configuration'
    _rec_name = 'name'

    name = fields.Char(
        string='Device Name',
        required=True,
        help='Friendly name for the ZKTeco device'
    )
    
    ip_address = fields.Char(
        string='IP Address',
        required=True,
        help='IP address of the ZKTeco device'
    )
    
    port = fields.Integer(
        string='Port',
        default=4370,
        required=True,
        help='Port number for device connection (default: 4370)'
    )
    
    timeout = fields.Integer(
        string='Connection Timeout',
        default=60,
        help='Connection timeout in seconds'
    )
    
    device_type = fields.Selection([
        ('zkteco', 'ZKTeco Standard'),
        ('zkteco_tcp', 'ZKTeco TCP/IP'),
    ], string='Device Type', default='zkteco', required=True)
    
    active = fields.Boolean(
        string='Active',
        default=True,
        help='Enable/disable this device for synchronization'
    )
    
    last_sync_date = fields.Datetime(
        string='Last Sync Date',
        readonly=True,
        help='Last successful synchronization date'
    )
    
    sync_interval = fields.Integer(
        string='Sync Interval (Minutes)',
        default=15,
        help='Automatic synchronization interval in minutes'
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )
    
    # Working hours configuration
    work_start_time = fields.Float(
        string='Work Start Time',
        default=8.0,
        help='Official work start time (24-hour format, e.g., 8.5 for 8:30 AM)'
    )
    
    work_end_time = fields.Float(
        string='Work End Time',
        default=17.0,
        help='Official work end time (24-hour format, e.g., 17.5 for 5:30 PM)'
    )
    
    late_tolerance = fields.Integer(
        string='Late Tolerance (Minutes)',
        default=15,
        help='Grace period in minutes before marking as late'
    )

    @api.constrains('ip_address')
    def _check_ip_address(self):
        """التحقق من صحة عنوان IP"""
        import re
        for record in self:
            if record.ip_address:
                ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
                if not re.match(ip_pattern, record.ip_address):
                    raise ValidationError(_('Please enter a valid IP address.'))

    @api.constrains('port')
    def _check_port(self):
        """التحقق من صحة رقم المنفذ"""
        for record in self:
            if record.port and (record.port < 1 or record.port > 65535):
                raise ValidationError(_('Port number must be between 1 and 65535.'))

    def test_connection(self):
        """اختبار الاتصال بالجهاز"""
        self.ensure_one()
        if not ZK:
            raise UserError(_('pyzk library is not installed. Please install it using: pip install pyzk'))
        
        try:
            zk = ZK(self.ip_address, port=self.port, timeout=self.timeout)
            conn = zk.connect()
            if conn:
                # Get device info
                firmware_version = conn.get_firmware_version()
                users_count = len(conn.get_users())
                conn.disconnect()
                
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Connection Successful'),
                        'message': _('Connected successfully!\nFirmware: %s\nUsers: %d') % (firmware_version, users_count),
                        'type': 'success',
                        'sticky': False,
                    }
                }
            else:
                raise UserError(_('Failed to connect to device'))
                
        except Exception as e:
            raise UserError(_('Connection failed: %s') % str(e))

    def sync_attendance_data(self):
        """مزامنة بيانات الحضور من الجهاز"""
        self.ensure_one()
        if not self.active:
            return
            
        if not ZK:
            _logger.error("pyzk library not found")
            return
            
        try:
            zk = ZK(self.ip_address, port=self.port, timeout=self.timeout)
            conn = zk.connect()
            if not conn:
                _logger.error("Failed to connect to device %s", self.name)
                return
                
            # Get attendance records
            attendances = conn.get_attendance()
            if not attendances:
                _logger.info("No attendance records found on device %s", self.name)
                conn.disconnect()
                return
                
            # Process attendance records
            processed_count = 0
            for attendance in attendances:
                if self._process_attendance_record(attendance):
                    processed_count += 1
                    
            # Update last sync date
            self.last_sync_date = fields.Datetime.now()
            
            conn.disconnect()
            _logger.info("Processed %d attendance records from device %s", processed_count, self.name)
            
        except Exception as e:
            _logger.error("Error syncing attendance data from device %s: %s", self.name, str(e))

    def _process_attendance_record(self, attendance_record):
        """معالجة سجل حضور واحد"""
        try:
            # Find employee by attendance PIN
            employee = self.env['hr.employee'].search([
                ('attendance_pin', '=', str(attendance_record.user_id)),
                ('company_id', '=', self.company_id.id)
            ], limit=1)
            
            if not employee:
                _logger.warning("Employee not found for PIN: %s", attendance_record.user_id)
                return False
                
            # Check if record already exists
            existing_record = self.env['hr.attendance'].search([
                ('employee_id', '=', employee.id),
                ('check_in', '=', attendance_record.timestamp),
                ('device_id', '=', self.id)
            ], limit=1)
            
            if existing_record:
                return False  # Skip duplicate
                
            # Create attendance record
            self.env['hr.attendance'].create({
                'employee_id': employee.id,
                'check_in': attendance_record.timestamp,
                'device_id': self.id,
            })
            
            return True
            
        except Exception as e:
            _logger.error("Error processing attendance record: %s", str(e))
            return False
