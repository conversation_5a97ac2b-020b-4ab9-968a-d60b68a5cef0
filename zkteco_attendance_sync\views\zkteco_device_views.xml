<?xml version="1.0" encoding="utf-8"?>
<!-- واجهات إعدادات أجهزة ZKTeco -->
<odoo>
    <data>
        
        <!-- Tree view for ZKTeco devices -->
        <record id="view_zkteco_device_tree" model="ir.ui.view">
            <field name="name">zkteco.device.tree</field>
            <field name="model">zkteco.device</field>
            <field name="arch" type="xml">
                <tree string="ZKTeco Devices">
                    <field name="name"/>
                    <field name="ip_address"/>
                    <field name="port"/>
                    <field name="device_type"/>
                    <field name="active"/>
                    <field name="last_sync_date"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>
        
        <!-- Form view for ZKTeco devices -->
        <record id="view_zkteco_device_form" model="ir.ui.view">
            <field name="name">zkteco.device.form</field>
            <field name="model">zkteco.device</field>
            <field name="arch" type="xml">
                <form string="ZKTeco Device Configuration">
                    <header>
                        <button name="test_connection" string="Test Connection" 
                                type="object" class="btn-primary"/>
                        <button name="sync_attendance_data" string="Sync Now" 
                                type="object" class="btn-secondary"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="toggle_active" type="object" 
                                    class="oe_stat_button" icon="fa-archive">
                                <field name="active" widget="boolean_button" 
                                       options='{"terminology": "archive"}'/>
                            </button>
                        </div>
                        
                        <group>
                            <group name="device_info" string="Device Information">
                                <field name="name"/>
                                <field name="device_type"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                            <group name="connection_info" string="Connection Settings">
                                <field name="ip_address"/>
                                <field name="port"/>
                                <field name="timeout"/>
                            </group>
                        </group>
                        
                        <group>
                            <group name="sync_settings" string="Synchronization Settings">
                                <field name="sync_interval"/>
                                <field name="last_sync_date" readonly="1"/>
                            </group>
                            <group name="work_hours" string="Working Hours Configuration">
                                <field name="work_start_time" widget="float_time"/>
                                <field name="work_end_time" widget="float_time"/>
                                <field name="late_tolerance"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Search view for ZKTeco devices -->
        <record id="view_zkteco_device_search" model="ir.ui.view">
            <field name="name">zkteco.device.search</field>
            <field name="model">zkteco.device</field>
            <field name="arch" type="xml">
                <search string="Search ZKTeco Devices">
                    <field name="name"/>
                    <field name="ip_address"/>
                    <field name="device_type"/>
                    <filter name="active" string="Active" domain="[('active', '=', True)]"/>
                    <filter name="inactive" string="Inactive" domain="[('active', '=', False)]"/>
                    <group expand="0" string="Group By">
                        <filter name="group_device_type" string="Device Type" 
                                context="{'group_by': 'device_type'}"/>
                        <filter name="group_company" string="Company" 
                                context="{'group_by': 'company_id'}" 
                                groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Action for ZKTeco devices -->
        <record id="action_zkteco_device" model="ir.actions.act_window">
            <field name="name">ZKTeco Devices</field>
            <field name="res_model">zkteco.device</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_zkteco_device_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first ZKTeco device configuration
                </p>
                <p>
                    Configure your ZKTeco biometric devices to automatically sync
                    attendance data with Odoo. You can add multiple devices and
                    set up automatic synchronization intervals.
                </p>
            </field>
        </record>
        
    </data>
</odoo>
