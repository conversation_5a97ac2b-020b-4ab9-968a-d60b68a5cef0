# -*- coding: utf-8 -*-
# نموذج تقارير الحضور والغياب

from odoo import models, fields, api, _
from datetime import datetime, timedelta
import calendar


class AttendanceReport(models.TransientModel):
    _name = 'attendance.report.wizard'
    _description = 'Attendance Report Wizard'

    date_from = fields.Date(
        string='From Date',
        required=True,
        default=lambda self: fields.Date.today().replace(day=1)
    )
    
    date_to = fields.Date(
        string='To Date',
        required=True,
        default=lambda self: fields.Date.today()
    )
    
    employee_ids = fields.Many2many(
        'hr.employee',
        string='Employees',
        help='Leave empty to include all employees'
    )
    
    department_ids = fields.Many2many(
        'hr.department',
        string='Departments',
        help='Filter by departments'
    )
    
    device_ids = fields.Many2many(
        'zkteco.device',
        string='Devices',
        help='Filter by specific devices'
    )
    
    report_type = fields.Selection([
        ('summary', 'Summary Report'),
        ('detailed', 'Detailed Report'),
        ('late_arrivals', 'Late Arrivals Only'),
        ('absences', 'Absences Only'),
    ], string='Report Type', default='summary', required=True)

    def generate_report(self):
        """إنشاء التقرير"""
        self.ensure_one()
        
        # Build domain for filtering
        domain = [
            ('check_in', '>=', self.date_from),
            ('check_in', '<=', self.date_to),
        ]
        
        if self.employee_ids:
            domain.append(('employee_id', 'in', self.employee_ids.ids))
        
        if self.device_ids:
            domain.append(('device_id', 'in', self.device_ids.ids))
            
        if self.department_ids:
            employees_in_dept = self.env['hr.employee'].search([
                ('department_id', 'in', self.department_ids.ids)
            ])
            domain.append(('employee_id', 'in', employees_in_dept.ids))
        
        # Additional filters based on report type
        if self.report_type == 'late_arrivals':
            domain.append(('is_late', '=', True))
        
        # Get attendance records
        attendances = self.env['hr.attendance'].search(domain)
        
        # Generate report data
        report_data = self._prepare_report_data(attendances)
        
        # Return report action
        return {
            'type': 'ir.actions.report',
            'report_name': 'zkteco_attendance_sync.attendance_report_template',
            'report_type': 'qweb-pdf',
            'data': report_data,
            'context': self.env.context,
        }

    def _prepare_report_data(self, attendances):
        """تحضير بيانات التقرير"""
        data = {
            'date_from': self.date_from,
            'date_to': self.date_to,
            'report_type': self.report_type,
            'employees_data': {},
            'summary': {
                'total_employees': 0,
                'total_present_days': 0,
                'total_late_arrivals': 0,
                'total_absences': 0,
            }
        }
        
        # Get all employees in scope
        if self.employee_ids:
            employees = self.employee_ids
        else:
            domain = []
            if self.department_ids:
                domain.append(('department_id', 'in', self.department_ids.ids))
            employees = self.env['hr.employee'].search(domain)
        
        # Calculate working days in period
        working_days = self._get_working_days(self.date_from, self.date_to)
        
        for employee in employees:
            emp_attendances = attendances.filtered(lambda a: a.employee_id.id == employee.id)
            
            # Calculate employee statistics
            present_days = len(emp_attendances.mapped('check_in').mapped(lambda d: d.date()))
            late_arrivals = len(emp_attendances.filtered('is_late'))
            absences = working_days - present_days
            
            data['employees_data'][employee.id] = {
                'employee': employee,
                'present_days': present_days,
                'late_arrivals': late_arrivals,
                'absences': absences,
                'attendance_records': emp_attendances,
                'attendance_percentage': (present_days / working_days * 100) if working_days > 0 else 0,
            }
            
            # Update summary
            data['summary']['total_present_days'] += present_days
            data['summary']['total_late_arrivals'] += late_arrivals
            data['summary']['total_absences'] += absences
        
        data['summary']['total_employees'] = len(employees)
        data['working_days'] = working_days
        
        return data

    def _get_working_days(self, date_from, date_to):
        """حساب أيام العمل في الفترة المحددة (باستثناء الجمعة والسبت)"""
        working_days = 0
        current_date = date_from
        
        while current_date <= date_to:
            # Skip Friday (4) and Saturday (5) - weekend in many Arab countries
            if current_date.weekday() not in [4, 5]:
                working_days += 1
            current_date += timedelta(days=1)
            
        return working_days

    def export_to_excel(self):
        """تصدير التقرير إلى Excel"""
        # This would require xlsxwriter or openpyxl
        # Implementation depends on your Excel export preferences
        pass
