<?xml version="1.0" encoding="utf-8"?>
<!-- ملف إعدادات المهام المجدولة (Cron Jobs) للمزامنة التلقائية -->
<odoo>
    <data noupdate="1">
        
        <!-- Cron job for automatic attendance synchronization -->
        <record id="cron_sync_attendance_data" model="ir.cron">
            <field name="name">ZKTeco Attendance Sync</field>
            <field name="model_id" ref="model_zkteco_device"/>
            <field name="state">code</field>
            <field name="code">
# مزامنة بيانات الحضور من جميع الأجهزة النشطة
devices = model.search([('active', '=', True)])
for device in devices:
    try:
        device.sync_attendance_data()
    except Exception as e:
        import logging
        _logger = logging.getLogger(__name__)
        _logger.error("Failed to sync device %s: %s", device.name, str(e))
            </field>
            <field name="interval_number">15</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="doall">False</field>
        </record>
        
    </data>
</odoo>
