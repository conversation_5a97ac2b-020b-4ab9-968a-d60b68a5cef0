# -*- coding: utf-8 -*-
# توسيع نموذج الموظف لإضافة رقم البصمة

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    attendance_pin = fields.Char(
        string='Attendance PIN',
        help='Employee PIN number used in biometric devices',
        copy=False
    )
    
    @api.constrains('attendance_pin')
    def _check_attendance_pin_unique(self):
        """التأكد من عدم تكرار رقم البصمة"""
        for employee in self:
            if employee.attendance_pin:
                duplicate = self.search([
                    ('attendance_pin', '=', employee.attendance_pin),
                    ('company_id', '=', employee.company_id.id),
                    ('id', '!=', employee.id)
                ])
                if duplicate:
                    raise ValidationError(
                        _('Attendance PIN "%s" is already used by employee "%s"') % 
                        (employee.attendance_pin, duplicate.name)
                    )
