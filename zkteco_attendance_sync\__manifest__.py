# -*- coding: utf-8 -*-
# ملف التعريف الرئيسي للموديول - يحتوي على معلومات الموديول والتبعيات
{
    'name': 'ZKTeco Attendance Sync',
    'version': '********.0',
    'category': 'Human Resources',
    'summary': 'Sync attendance data from ZKTeco biometric devices',
    'description': """
        ZKTeco Attendance Synchronization Module
        ========================================
        
        This module provides integration with ZKTeco biometric devices to automatically
        sync attendance records with Odoo HR Attendance module.
        
        Features:
        ---------
        * Connect to multiple ZKTeco devices via IP/Port
        * Automatic attendance data synchronization
        * Configurable sync intervals via cron jobs
        * Employee mapping based on attendance PIN
        * Duplicate record prevention
        * Attendance reports with late arrival and absence tracking
        * Configurable working hours and tolerance settings
        
        Requirements:
        -------------
        * pyzk library for ZKTeco device communication
        * Network connectivity to ZKTeco devices
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'hr',
        'hr_attendance',
    ],
    'external_dependencies': {
        'python': ['pyzk'],
    },
    'data': [
        'security/ir.model.access.csv',
        'data/cron_data.xml',
        'views/zkteco_device_views.xml',
        'views/hr_employee_views.xml',
        'views/attendance_report_views.xml',
        'views/menu_views.xml',
    ],
    'demo': [],
    'installable': True,
    'auto_install': False,
    'application': False,
    'sequence': 100,
}
